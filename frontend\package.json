{"name": "hdsc_query_app_frontend", "version": "2.0.0", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore"}, "dependencies": {"vue": "^3.4.0", "vue-router": "^4.2.0", "pinia": "^2.1.0", "axios": "^1.6.0", "@vueuse/core": "^10.7.0", "element-plus": "^2.4.0", "@element-plus/icons-vue": "^2.3.0", "chart.js": "^4.4.0", "vue-chartjs": "^5.3.0", "chartjs-adapter-date-fns": "^3.0.0", "date-fns": "^3.0.0"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.0", "vite": "^5.0.0", "typescript": "^5.3.0", "@types/node": "^20.10.0", "tailwindcss": "^3.4.0", "autoprefixer": "^10.4.0", "postcss": "^8.4.0", "vue-tsc": "^1.8.0", "eslint": "^8.57.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "eslint-plugin-vue": "^9.17.0", "vite-plugin-pwa": "^0.17.0"}}